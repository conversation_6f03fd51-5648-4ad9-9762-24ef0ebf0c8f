import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'page_translation.g.dart';

/// State class for page translation interface
class PageTranslationState {
  const PageTranslationState({
    this.currentPageContent = '',
    this.translatedContent = '',
    this.selectedLanguage,
    this.isTranslating = false,
    this.error,
    this.isVisible = false,
  });

  final String currentPageContent;
  final String translatedContent;
  final LangList? selectedLanguage;
  final bool isTranslating;
  final String? error;
  final bool isVisible;

  PageTranslationState copyWith({
    String? currentPageContent,
    String? translatedContent,
    LangList? selectedLanguage,
    bool? isTranslating,
    String? error,
    bool? isVisible,
  }) {
    return PageTranslationState(
      currentPageContent: currentPageContent ?? this.currentPageContent,
      translatedContent: translatedContent ?? this.translatedContent,
      selectedLanguage: selectedLanguage ?? this.selectedLanguage,
      isTranslating: isTranslating ?? this.isTranslating,
      error: error,
      isVisible: isVisible ?? this.isVisible,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PageTranslationState &&
        other.currentPageContent == currentPageContent &&
        other.translatedContent == translatedContent &&
        other.selectedLanguage == selectedLanguage &&
        other.isTranslating == isTranslating &&
        other.error == error &&
        other.isVisible == isVisible;
  }

  @override
  int get hashCode {
    return Object.hash(
      currentPageContent,
      translatedContent,
      selectedLanguage,
      isTranslating,
      error,
      isVisible,
    );
  }
}

@Riverpod(keepAlive: true)
class PageTranslation extends _$PageTranslation {
  static const String _selectedLanguageKey = 'page_translation_language';

  @override
  PageTranslationState build() {
    // Initialize with saved language preference
    final savedLanguageCode = Prefs().prefs.getString(_selectedLanguageKey);
    final selectedLanguage = savedLanguageCode != null
        ? getLang(savedLanguageCode)
        : LangList.english; // Default to English

    return PageTranslationState(
      selectedLanguage: selectedLanguage,
    );
  }

  /// Show the translation interface
  void showTranslationInterface() {
    state = state.copyWith(isVisible: true, error: null);
  }

  /// Hide the translation interface
  void hideTranslationInterface() {
    state = state.copyWith(isVisible: false, error: null);
  }

  /// Set the current page content to be translated
  void setCurrentPageContent(String content) {
    if (content.trim().isEmpty) {
      state = state.copyWith(
        currentPageContent: '',
        translatedContent: '',
        error: 'No content to translate',
      );
      return;
    }

    state = state.copyWith(
      currentPageContent: content,
      translatedContent: '', // Clear previous translation
      error: null,
    );

    // Auto-translate if interface is visible and language is selected
    if (state.isVisible && state.selectedLanguage != null) {
      translateCurrentContent();
    }
  }

  /// Change the target translation language
  Future<void> changeLanguage(LangList language) async {
    if (language == state.selectedLanguage) return;

    try {
      // Save language preference
      await Prefs().prefs.setString(_selectedLanguageKey, language.code);

      // Update state with new language
      state = state.copyWith(
        selectedLanguage: language,
        translatedContent: '', // Clear previous translation
        error: null,
        isTranslating: false, // Reset translating state
      );

      AnxLog.info('Translation language changed to: ${language.code}');

      // Auto-translate if we have content
      if (state.currentPageContent.isNotEmpty) {
        await translateCurrentContent();
      }
    } catch (e) {
      AnxLog.severe('Failed to change translation language: $e');
      state = state.copyWith(
        error: 'Failed to change language',
        isTranslating: false,
      );
    }
  }

  /// Translate the current page content
  Future<void> translateCurrentContent() async {
    if (state.currentPageContent.trim().isEmpty) {
      state = state.copyWith(error: 'No content to translate');
      return;
    }

    if (state.selectedLanguage == null) {
      state = state.copyWith(error: 'Please select a target language');
      return;
    }

    state = state.copyWith(isTranslating: true, error: null);

    try {
      // Use existing translation service with custom language settings
      final originalFrom = Prefs().translateFrom;
      final originalTo = Prefs().translateTo;

      // Temporarily set translation preferences for page translation
      Prefs().translateFrom = LangList.auto; // Auto-detect source language
      Prefs().translateTo = state.selectedLanguage!;

      final translatedText = await translateText(state.currentPageContent);

      // Restore original translation preferences
      Prefs().translateFrom = originalFrom;
      Prefs().translateTo = originalTo;

      state = state.copyWith(
        translatedContent: translatedText,
        isTranslating: false,
        error: null,
      );

      AnxLog.info('Page translation completed successfully');
    } catch (e) {
      AnxLog.severe('Page translation failed: $e');
      state = state.copyWith(
        isTranslating: false,
        error: 'Translation failed: ${e.toString()}',
      );
    }
  }

  /// Retry translation
  Future<void> retryTranslation() async {
    await translateCurrentContent();
  }

  /// Clear all translation data
  void clearTranslation() {
    state = state.copyWith(
      currentPageContent: '',
      translatedContent: '',
      error: null,
      isTranslating: false,
    );
  }

  /// Get available target languages (excluding auto and current source)
  List<LangList> getAvailableTargetLanguages() {
    return LangList.values.where((lang) => lang != LangList.auto).toList();
  }
}
