import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/providers/page_translation.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Translation modal bottom sheet widget for the reading page
class TranslationModal extends ConsumerWidget {
  const TranslationModal({
    super.key,
    required this.onNavigatePrevious,
    required this.onNavigateNext,
    required this.backgroundColor,
    required this.textColor,
  });

  final VoidCallback onNavigatePrevious;
  final VoidCallback onNavigateNext;
  final Color backgroundColor;
  final Color textColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final translationState = ref.watch(pageTranslationProvider);

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        minHeight: MediaQuery.of(context).size.height * 0.4,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(DesignSystem.radiusM),
          topRight: Radius.circular(DesignSystem.radiusM),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and close button
          _buildHeader(context, ref),

          // Language selection dropdown
          _buildLanguageSelector(context, ref, translationState),

          // Translated content area
          Expanded(
            child: _buildContentArea(context, ref, translationState),
          ),

          // Navigation controls
          _buildNavigationControls(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Container(
      padding: DesignSystem.containerPaddingMedium,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: textColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              L10n.of(context).translation_interface_title,
              style: TextStyle(
                fontSize: DesignSystem.fontSizeL,
                fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
                color: textColor,
              ),
            ),
          ),
          SemanticHelpers.button(
            context: context,
            label: L10n.of(context).translation_interface_close,
            hint: L10n.of(context).translation_interface_close,
            onTap: () => ref
                .read(pageTranslationProvider.notifier)
                .hideTranslationInterface(),
            child: IconButton(
              onPressed: () => ref
                  .read(pageTranslationProvider.notifier)
                  .hideTranslationInterface(),
              icon: Icon(
                Icons.close,
                color: textColor,
                size: DesignSystem.getAdjustedIconSize(24.0),
              ),
              tooltip: L10n.of(context).translation_interface_close,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector(
    BuildContext context,
    WidgetRef ref,
    PageTranslationState state,
  ) {
    return Container(
      padding: DesignSystem.containerPaddingMedium,
      child: Row(
        children: [
          Text(
            L10n.of(context).translation_interface_select_language,
            style: TextStyle(
              fontSize: DesignSystem.fontSizeM,
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
              color: textColor,
            ),
          ),
          DesignSystem.horizontalSpaceM,
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignSystem.spaceM,
                vertical: DesignSystem.spaceS,
              ),
              decoration: BoxDecoration(
                border: Border.all(color: textColor.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(DesignSystem.radiusS),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<LangList>(
                  value: state.selectedLanguage,
                  isExpanded: true,
                  dropdownColor: backgroundColor,
                  style: TextStyle(color: textColor),
                  icon: Icon(Icons.arrow_drop_down, color: textColor),
                  items: ref
                      .read(pageTranslationProvider.notifier)
                      .getAvailableTargetLanguages()
                      .map(
                        (language) => DropdownMenuItem<LangList>(
                          value: language,
                          child: Text(
                            language.getNative(context),
                            style: TextStyle(color: textColor),
                          ),
                        ),
                      )
                      .toList(),
                  onChanged: (LangList? newLanguage) {
                    if (newLanguage != null &&
                        newLanguage != state.selectedLanguage) {
                      // Prevent multiple rapid calls
                      Future.microtask(() async {
                        try {
                          await ref
                              .read(pageTranslationProvider.notifier)
                              .changeLanguage(newLanguage);
                          if (context.mounted) {
                            AnxToast.show(
                              L10n.of(context)
                                  .translation_interface_language_changed,
                            );
                          }
                        } catch (e) {
                          AnxLog.severe(
                            'Failed to change language in dropdown: $e',
                          );
                          if (context.mounted) {
                            AnxToast.show('Failed to change language');
                          }
                        }
                      });
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentArea(
    BuildContext context,
    WidgetRef ref,
    PageTranslationState state,
  ) {
    if (state.isTranslating) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(textColor),
            ),
            DesignSystem.verticalSpaceM,
            Text(
              L10n.of(context).translation_interface_translating,
              style: TextStyle(
                color: textColor,
                fontSize: DesignSystem.fontSizeM,
              ),
            ),
          ],
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: textColor.withValues(alpha: 0.7),
              size: DesignSystem.getAdjustedIconSize(48.0),
            ),
            DesignSystem.verticalSpaceM,
            Text(
              state.error!,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: DesignSystem.fontSizeM,
              ),
              textAlign: TextAlign.center,
            ),
            DesignSystem.verticalSpaceM,
            ElevatedButton(
              onPressed: () =>
                  ref.read(pageTranslationProvider.notifier).retryTranslation(),
              style: ElevatedButton.styleFrom(
                backgroundColor: textColor.withValues(alpha: 0.1),
                foregroundColor: textColor,
              ),
              child: Text(L10n.of(context).translation_interface_retry),
            ),
          ],
        ),
      );
    }

    if (state.translatedContent.isEmpty) {
      return Center(
        child: Text(
          L10n.of(context).translation_interface_no_content,
          style: TextStyle(
            color: textColor.withValues(alpha: 0.7),
            fontSize: DesignSystem.fontSizeM,
          ),
        ),
      );
    }

    return Container(
      padding: DesignSystem.containerPaddingMedium,
      child: SingleChildScrollView(
        child: SelectableText(
          state.translatedContent,
          style: TextStyle(
            color: textColor,
            fontSize: DesignSystem.fontSizeM,
            height: 1.6,
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationControls(BuildContext context) {
    return Container(
      padding: DesignSystem.containerPaddingMedium,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: textColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: SemanticHelpers.button(
              context: context,
              label: L10n.of(context).translation_interface_previous,
              hint: L10n.of(context).translation_interface_previous,
              onTap: onNavigatePrevious,
              child: ElevatedButton(
                onPressed: onNavigatePrevious,
                style: ElevatedButton.styleFrom(
                  backgroundColor: textColor.withValues(alpha: 0.1),
                  foregroundColor: textColor,
                  minimumSize:
                      const Size.fromHeight(DesignSystem.widgetMinTouchTarget),
                ),
                child: Text(L10n.of(context).translation_interface_previous),
              ),
            ),
          ),
          DesignSystem.horizontalSpaceM,
          Expanded(
            child: SemanticHelpers.button(
              context: context,
              label: L10n.of(context).translation_interface_next,
              hint: L10n.of(context).translation_interface_next,
              onTap: onNavigateNext,
              child: ElevatedButton(
                onPressed: onNavigateNext,
                style: ElevatedButton.styleFrom(
                  backgroundColor: textColor.withValues(alpha: 0.1),
                  foregroundColor: textColor,
                  minimumSize:
                      const Size.fromHeight(DesignSystem.widgetMinTouchTarget),
                ),
                child: Text(L10n.of(context).translation_interface_next),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
